/**
 * 幼儿信息管理页面
 */

const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    action: 'list', // list: 列表, add: 添加, edit: 编辑
    children: [],
    currentChild: null,
    fromOnboarding: false,
    
    // 表单数据
    formData: {
      name: '',
      birthday: '',
      classType: ''
    },
    
    // 选择器数据
    years: [],
    months: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
    classTypes: ['小班', '中班', '大班'],
    
    // 选择器索引
    yearIndex: null,
    monthIndex: null,
    classIndex: null
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    const action = options.action || 'list';
    const fromOnboarding = options.fromOnboarding === 'true';
    
    this.setData({
      action,
      fromOnboarding
    });

    this.initYears();
    this.loadChildren();
    
    if (action === 'add' && fromOnboarding) {
      this.setData({
        yearIndex: null,
        monthIndex: null,
        classIndex: null
      });
    } else if (action === 'edit' && options.childId) {
      this.loadChildForEdit(options.childId);
    }
  },

  /**
   * 初始化年份数据
   */
  initYears: function() {
    const currentYear = new Date().getFullYear();
    const years = [];
    
    for (let i = 0; i < 10; i++) {
      years.push(`${currentYear - i}年`);
    }
    
    this.setData({
      years
    });
  },

  /**
   * 加载幼儿列表
   */
  loadChildren: function() {
    const userInfo = app.dataManager.getUserInfo();
    if (userInfo && userInfo.children_info) {
      this.setData({
        children: userInfo.children_info
      });
    }
  },

  /**
   * 加载要编辑的幼儿信息
   */
  loadChildForEdit: function(childId) {
    const userInfo = app.dataManager.getUserInfo();
    if (!userInfo) return;
    
    const child = userInfo.children_info.find(c => c.child_id === childId);
    if (!child) return;
    
    let year = '';
    let month = '';
    let yearIndex = null;
    let monthIndex = null;
    let formattedBirthday = null; // Will store YYYY-MM or null
    let calculatedAge = null; // Will store age or null

    // Attempt to parse existing birthday
    if (child.birthday && child.birthday.includes('-')) {
      const parts = child.birthday.split('-');
      year = parts[0];
      month = parts[1];

      yearIndex = this.data.years.findIndex(y => y.startsWith(year));
      
      const parsedMonth = parseInt(month);
      // Check if month is a valid number between 1 and 12
      if (!isNaN(parsedMonth) && parsedMonth >= 1 && parsedMonth <= 12) {
        monthIndex = parsedMonth - 1;
        formattedBirthday = `${year}-${String(parsedMonth).padStart(2, '0')}`;
        calculatedAge = app.dataManager.calculateAge(formattedBirthday);
      } else {
        // If month is invalid, set indices and birthday to null
        yearIndex = null;
        monthIndex = null;
        formattedBirthday = null;
        calculatedAge = null;
      }
    } else {
      // If birthday format is completely off, set everything to null
      yearIndex = null;
      monthIndex = null;
      formattedBirthday = null;
      calculatedAge = null;
    }

    const classIndex = this.data.classTypes.indexOf(child.class_type);
    
    this.setData({
      currentChild: child,
      formData: {
        name: child.child_name,
        birthday: formattedBirthday, // Use null if invalid
        classType: child.class_type,
        age: calculatedAge // Use null if invalid
      },
      yearIndex: yearIndex >= 0 ? yearIndex : null,
      monthIndex: monthIndex >= 0 ? monthIndex : null,
      classIndex: classIndex >= 0 ? classIndex : null
    });
  },

  /**
   * 切换到添加模式
   */
  switchToAdd: function() {
    this.setData({
      action: 'add',
      formData: {
        name: '',
        birthday: '',
        classType: ''
      },
      yearIndex: null,
      monthIndex: null,
      classIndex: null
    });
  },

  /**
   * 切换到列表模式
   */
  switchToList: function() {
    this.setData({
      action: 'list'
    });
  },

  /**
   * 编辑幼儿
   */
  editChild: function(e) {
    const childId = e.currentTarget.dataset.childId;
    this.setData({
      action: 'edit'
    });
    this.loadChildForEdit(childId);
  },

  /**
   * 删除幼儿
   */
  deleteChild: function(e) {
    const childId = e.currentTarget.dataset.childId;
    const child = this.data.children.find(c => c.child_id === childId);
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除 ${child.child_name} 的所有信息吗？此操作不可恢复。`,
      confirmText: '删除',
      confirmColor: '#ff4444',
      success: (res) => {
        if (res.confirm) {
          this.performDeleteChild(childId);
        }
      }
    });
  },

  /**
   * 执行删除幼儿
   */
  performDeleteChild: function(childId) {
    wx.showLoading({
      title: '删除中...'
    });

    const userInfo = app.dataManager.getUserInfo();
    if (!userInfo) {
      wx.hideLoading();
      return;
    }

    // 删除幼儿信息
    userInfo.children_info = userInfo.children_info.filter(c => c.child_id !== childId);
    
    // 删除相关数据
    const wordLibrary = app.dataManager.getWordLibrary();
    const filteredWords = wordLibrary.filter(word => word.child_id !== childId);
    app.dataManager.saveWordLibrary(filteredWords);
    
    const learningRecords = app.dataManager.getLearningRecords();
    const filteredRecords = learningRecords.filter(record => record.child_id !== childId);
    wx.setStorageSync('learning_records', filteredRecords);
    
    const reviewPlans = app.dataManager.getReviewPlans();
    const filteredPlans = reviewPlans.filter(plan => plan.child_id !== childId);
    app.dataManager.saveReviewPlans(filteredPlans);

    // 保存用户信息
    app.dataManager.saveUserInfo(userInfo);
    
    // 如果删除的是当前选中的幼儿，切换到第一个
    const currentChild = app.getCurrentChild();
    if (currentChild && currentChild.child_id === childId) {
      const newCurrentChild = userInfo.children_info[0] || null;
      app.setCurrentChild(newCurrentChild);
    }

    wx.hideLoading();
    this.loadChildren();
    
    wx.showToast({
      title: '删除成功',
      icon: 'success'
    });
  },

  /**
   * 选择当前幼儿
   */
  selectChild: function(e) {
    const childId = e.currentTarget.dataset.childId;
    const child = this.data.children.find(c => c.child_id === childId);
    
    app.setCurrentChild(child);
    
    wx.showToast({
      title: `已选择 ${child.child_name}`,
      icon: 'success'
    });

    // 如果来自引导页，跳转到首页
    if (this.data.fromOnboarding) {
      setTimeout(() => {
        wx.switchTab({
          url: '/pages/home/<USER>'
        });
      }, 1500);
    }
  },

  /**
   * 表单输入
   */
  onNameInput: function(e) {
    const inputValue = e.detail.value;
    console.log('[幼儿姓名输入] 原始输入值:', inputValue);
    const trimmedValue = inputValue.trim();
    console.log('[幼儿姓名输入] 处理后值:', trimmedValue);
    this.setData({
      'formData.name': trimmedValue // 移除首尾空格
    });
  },

  /**
   * 年份选择
   */
  onYearChange: function(e) {
    const yearIndex = e.detail.value;
    this.setData({
      yearIndex
    });
    this.updateBirthday();
  },

  /**
   * 月份选择
   */
  onMonthChange: function(e) {
    const monthIndex = e.detail.value;
    this.setData({
      monthIndex
    });
    this.updateBirthday();
  },

  /**
   * 班级选择
   */
  onClassChange: function(e) {
    const classIndex = e.detail.value;
    this.setData({
      classIndex,
      'formData.classType': this.data.classTypes[classIndex]
    });
  },

  /**
   * 更新生日
   */
  updateBirthday: function() {
    // 验证年份和月份索引
    if (this.data.yearIndex === null || this.data.monthIndex === null) {
      this.setData({
        'formData.birthday': '',
        'formData.age': null
      });
      return;
    }

    // 验证月份索引范围 (0-11)
    if (this.data.monthIndex < 0 || this.data.monthIndex > 11) {
      this.setData({
        'formData.birthday': '',
        'formData.age': null
      });
      return;
    }

    const year = this.data.years[this.data.yearIndex].replace('年', '');
    const monthValue = this.data.months[this.data.monthIndex].replace('月', '');
    const month = monthValue.padStart(2, '0');
    const birthday = `${year}-${month}`;
    const age = app.dataManager.calculateAge(birthday);
    
    this.setData({
      'formData.birthday': birthday,
      'formData.age': age
    });
  },

  /**
   * 保存幼儿信息
   */
  saveChild: function() {
    const { name, birthday, classType } = this.data.formData;
    
    if (!name) {
      wx.showToast({
        title: '请输入幼儿姓名',
        icon: 'none'
      });
      return;
    }

    if (this.data.yearIndex === null || this.data.monthIndex === null) {
      wx.showToast({
        title: '请选择出生年月',
        icon: 'none'
      });
      return;
    }

    if (!birthday) {
      wx.showToast({
        title: '请选择生日',
        icon: 'none'
      });
      return;
    }

    if (!classType) {
      wx.showToast({
        title: '请选择班级',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '保存中...'
    });

    if (this.data.action === 'add') {
      this.addNewChild();
    } else {
      this.updateChild();
    }
  },

  /**
   * 添加新幼儿
   */
  addNewChild: function() {
    console.log('addNewChild called, formData:', this.data.formData);
    const childInfo = {
      name: this.data.formData.name.trim(),
      birthday: this.data.formData.birthday,
      classType: this.data.formData.classType,
      age: this.data.formData.age
    };

    const success = app.dataManager.addChild(childInfo);
    
    wx.hideLoading();

    if (success) {
      wx.showToast({
        title: '添加成功',
        icon: 'success'
      });

      // 设置为当前幼儿
      const userInfo = app.dataManager.getUserInfo();
      const newChild = userInfo.children_info[userInfo.children_info.length - 1];
      app.setCurrentChild(newChild);

      this.loadChildren();
      
      if (this.data.fromOnboarding) {
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/home/<USER>'
          });
        }, 1500);
      } else {
        this.switchToList();
      }
    } else {
      wx.showToast({
        title: '添加失败',
        icon: 'none'
      });
    }
  },

  /**
   * 更新幼儿信息
   */
  updateChild: function() {
    const userInfo = app.dataManager.getUserInfo();
    if (!userInfo) {
      wx.hideLoading();
      return;
    }

    const childIndex = userInfo.children_info.findIndex(c => c.child_id === this.data.currentChild.child_id);
    if (childIndex === -1) {
      wx.hideLoading();
      return;
    }

    // 更新幼儿信息
    userInfo.children_info[childIndex].child_name = this.data.formData.name.trim();
    userInfo.children_info[childIndex].birthday = this.data.formData.birthday;
    userInfo.children_info[childIndex].class_type = this.data.formData.classType;
    userInfo.children_info[childIndex].age = app.dataManager.calculateAge(this.data.formData.birthday);

    const success = app.dataManager.saveUserInfo(userInfo);
    
    wx.hideLoading();

    if (success) {
      wx.showToast({
        title: '更新成功',
        icon: 'success'
      });

      // 如果更新的是当前选中的幼儿，更新全局状态
      const currentChild = app.getCurrentChild();
      if (currentChild && currentChild.child_id === this.data.currentChild.child_id) {
        app.setCurrentChild(userInfo.children_info[childIndex]);
      }

      this.loadChildren();
      this.switchToList();
    } else {
      wx.showToast({
        title: '更新失败',
        icon: 'none'
      });
    }
  }
});