# 小柿子・幼儿识字辅助工具详细功能需求文档（最终优化版）

## 一、项目概述

“小柿子・幼儿识字辅助工具” 专为家长与幼儿打造高效识字复习平台，基于微信小程序开发与上线，以简洁界面与实用功能为核心，结合幼儿园教学内容与艾宾浩斯遗忘曲线规律，助力幼儿快速巩固字词知识，同时引导家长科学参与孩子的学习过程。所有数据均仅存储于用户设备本地，不涉及联网保存，保障数据私密性，但需注意本地数据无备份机制，一旦丢失无法恢复。

## 二、功能需求详述

### （一）新手引导页面

1.  **核心开发目的展示**：以全屏大图配简洁文字的形式，突出呈现工具开发目的。主标题使用大字号加粗字体显示 “助力幼儿高效识字，让亲子学习更轻松” ，下方用简洁易懂的文字说明：“本工具旨在帮助家长陪伴孩子高效复习所学字词，结合艾宾浩斯遗忘曲线科学规划学习，告别枯燥机械记忆，用最少的时间收获最佳学习效果。”

2.  **操作指引**：通过图文结合的方式，简单演示微信小程序基础操作流程，如添加字词、进行复习、查看学习记录等。同时说明小程序无需下载，可直接在微信内使用的便捷特性。

3.  **确认机制**：设置 “我已了解” 确认按钮，用户点击后方可进入小程序主界面。

### （二）当日字词复习模块

1.  **内容同步机制**：支持家长手动录入当日幼儿园所学字词，或通过预设模板批量导入，快速完成学习内容更新。数据实时保存在本地设备，支持离线操作，且需符合微信小程序的数据存储限制规范。

2.  **高效学习模式**

*   **大字显示界面**：以大字形式展示汉字，字体采用标准楷体，字号确保在各类移动设备上清晰可见，单屏仅显示一个字词，避免信息干扰。界面设计需遵循微信小程序设计规范，确保操作按钮布局合理、易于点击。

*   **快速检测功能**：家长与幼儿共同判断字词掌握情况，通过勾选 “已掌握”“需复习” 快速标记，每次复习限定 10 - 15 个字词，控制学习时长。操作记录即时存储于本地，支持随时查看历史标记状态。

1.  **学习记录与反馈**

*   自动记录每日复习的字词数量、掌握情况，生成本地数据看板，家长可离线查看幼儿学习进度。数据展示需适配微信小程序的界面尺寸与交互逻辑。

*   支持将本地学习记录分享到微信朋友圈和微信好友，分享功能需严格遵守微信小程序的分享接口规则与审核要求。

### （三）历史字词复习模块

1.  **智能复习规划**：基于艾宾浩斯遗忘曲线算法，在本地设备内自动规划历史字词复习计划，按 “天 - 周 - 月” 周期推送复习任务，每次复习任务控制在 5 - 10 分钟内。复习计划数据存储于本地，支持家长手动修改或暂停。开发过程中需确保算法逻辑在微信小程序环境下稳定运行。

2.  **精准效果评估**

*   记录每次复习的正确率、标记的薄弱字词，在本地生成个性化复习建议文档。

*   家长可手动调整复习计划，针对幼儿易错字词增加复习频次，调整记录实时保存于本地。

### （四）家长管理与辅助功能

1.  **个性化设置**

*   **生日与年龄设置**：新增幼儿生日（年 - 月）设置选项，采用下拉选择框形式，年份范围限定为当前年份往前推 10 年，月份为 1 - 12 月。用户设置生日后，小程序自动获取当前日期，通过计算当前年份与设置年份的差值，若当前月份小于设置月份，则年龄减 1，从而自动推算出幼儿年龄，并在年龄显示区域实时更新。例如，若设置生日为 2018 年 5 月，当前日期为 2024 年 3 月，则显示年龄为 5 岁；若当前日期为 2024 年 6 月，则显示年龄为 6 岁。

*   家长可在系统中灵活设置幼儿所在班级，支持小班、中班、大班三种选项，相关信息仅存储在本地设备。设置操作需符合微信小程序的交互规范，提供清晰的操作反馈。

*   支持多幼儿账号管理，每个账号数据独立保存在本地，满足二胎家庭需求。

1.  **学习资源管理**

*   提供字词库编辑功能，家长可添加自定义字词、标记重点内容，或删除重复、不适用字词。字词库数据全程存储于本地，不支持本地备份与恢复。

1.  **亲子互动引导**

*   内置 “家长指导手册” 以本地文件形式存储，提供科学识字方法与互动技巧建议，支持离线阅读。文件格式需适配微信小程序的本地存储与读取要求。

*   设计亲子任务清单（如 “和孩子一起朗读本周复习字词”、“找到家里某个字词”），任务进度记录保存在本地，鼓励家长深度参与学习过程。

### （五）设置页面

在设置页面底部以灰色小字显示提示信息：“本工具所有学习数据仅保存在您的设备本地，一旦删除本程序或设备故障导致数据丢失，无法通过任何方式找回”，确保用户在进行账号管理、清除缓存等操作时能注意到数据风险。设置页面的样式与交互需遵循微信小程序官方设计规范，保证用户操作体验一致性。

## 三、非功能需求

1.  **兼容性需求**：兼容微信 7.0 及以上版本，适配主流移动设备（安卓、iOS），确保不同屏幕尺寸下汉字显示完整、界面操作便捷，且本地数据存储与读取功能在各设备稳定运行。同时需通过微信小程序的兼容性测试要求，保证在不同微信版本、机型上正常使用。

2.  **界面设计原则**：采用低饱和度、护眼色调，界面布局极简，仅保留必要操作按钮，最大化突出汉字显示区域。所有界面元素本地加载，不依赖网络。界面设计需严格遵循《微信小程序设计指南》，使用微信官方提供的图标、组件等设计资源，保持与微信生态的视觉一致性。

3.  **数据安全需求**：所有数据仅存储于用户设备本地，不提供联网上传、云端存储功能，无数据泄露风险。支持家长手动删除本地数据，删除后无法恢复；同时在设置页面等关键位置，再次提示数据无备份的风险。开发过程中需遵循微信小程序的数据安全规范，确保用户数据存储、处理符合相关要求。
