<!--添加字词页面-->
<view class="add-words-container">
  <!-- 幼儿信息 -->
  <view class="child-info-bar">
    <view class="child-avatar">👶</view>
    <view class="child-details">
      <view class="child-name">{{currentChild.child_name}}</view>
      <view class="child-class">{{currentChild.class_type}}</view>
    </view>
  </view>

  <!-- 批量输入区域 -->
  <view class="input-section">
    <view class="section-title">📋 批量添加</view>
    <view class="batch-input-container">
      <textarea 
        class="batch-input"
        placeholder="请输入多个字词，支持用逗号、顿号、空格或换行分隔&#10;例如：太阳，月亮，星星"
        value="{{inputText}}"
        bindinput="onInputChange"
        maxlength="500"
        auto-height
      ></textarea>
    </view>
    
    <view class="separator-tips">
      <text class="tips-title">支持的分隔符：</text>
      <view class="separator-list">
        <text class="separator-item" wx:for="{{separators}}" wx:key="index">
          {{item === '\n' ? '换行' : item}}
        </text>
      </view>
    </view>
    
    <view class="action-buttons">
      <button class="btn-outline" bindtap="useExample">使用示例</button>
      <button class="btn-outline" bindtap="clearInput">清空</button>
      <button class="btn-primary" bindtap="addBatchWords">批量添加</button>
    </view>
  </view>

  <!-- 字词预览 -->
  <view class="preview-section" wx:if="{{showPreview}}">
    <view class="section-title">👀 字词预览 ({{wordList.length}}个)</view>
    <view class="word-preview-list">
      <view 
        wx:for="{{wordList}}" 
        wx:key="index" 
        class="preview-word-item"
      >
        <text class="word-text">{{item}}</text>
        <button 
          class="remove-word-btn" 
          data-index="{{index}}"
          bindtap="removeWord"
        >
          ✕
        </button>
      </view>
    </view>
  </view>

  <!-- 使用说明 -->
  <view class="usage-guide">
    <view class="guide-title">💡 使用说明</view>
    <view class="guide-content">
      <view class="guide-item">• 批量添加：支持一次性添加多个字词</view>
      <view class="guide-item">• 自动去重：重复的字词会被自动过滤</view>
      <view class="guide-item">• 添加后会自动生成复习计划</view>
    </view>
  </view>

  <!-- 底部操作 -->
  <view class="bottom-actions">
    <button class="btn-outline view-library-btn" bindtap="viewWordLibrary">
      📚 查看字词库
    </button>
  </view>
</view>
